<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * Description of Student_leave_controller
 *
 * <AUTHOR>
 */
class Student_leave_controller extends CI_Controller {

    function __construct() {
    
        parent::__construct();
        if (!$this->ion_auth->logged_in()) {
            redirect('auth/login', 'refresh');
        }
        if (!$this->authorization->isModuleEnabled('STUDENT_LEAVE')) {
            redirect('dashboard', 'refresh');
          }
        $this->load->model('student/Student_leave_model');
        $this->load->model('avatar');
        $this->load->model('observation_model');
        $this->load->model('student/Student_Model');
        $this->load->model('parent_model');
        $this->load->library('filemanager');
    }
   

    public function index() {
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        //echo $data['avatar']->stakeholderId; die();
        // $stdyearId = $this->Student_leave_model->getStdIdByParentId($avatar->stakeholderId);

        $studentId = $this->parent_model->getStudentIdOfLoggedInParent();

        $data['approved'] = $this->Student_leave_model->getCountOfLeaves($studentId, 0);
        $data['applied'] = $this->Student_leave_model->getCountOfLeaves($studentId, 1);
        $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaveInfo($studentId);
       // echo "<pre>";print_r($data['getFullStudentDetail']);die();
        $data['main_content'] = 'student/student_leave/student_leave_index';
        $this->load->view('inc/template', $data);
    }

    // Get holiday count using calendar v2 system for student leave
    public function getHolidayCountV2() {
        if (!isset($_POST['startDate']) || !isset($_POST['endDate']) || !isset($_POST['studentId'])) {
            echo 0;
            return;
        }

        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];
        $student_id = $_POST['studentId'];

        if (empty($start_date) || empty($end_date) || empty($student_id)) {
            echo 0;
            return;
        }

        // Get student's section ID
        $section_id = $this->Student_leave_model->getStudentSectionId($student_id);

        if (!$section_id) {
            // No section found, fall back to basic calculation
            echo $this->_calculateDaysExcludingWeekends($start_date, $end_date);
            return;
        }

        // Load the Student_Model to get assigned calendar details
        $this->load->model('student/Student_Model');
        $calendar_details = $this->Student_Model->getassignedcalendardetails($section_id);

        if (!$calendar_details) {
            // No calendar assigned, fall back to old method
            echo $this->_calculateDaysExcludingWeekends($start_date, $end_date);
            return;
        }

        // Check if leave dates are within calendar range
        $calendar_start = new DateTime($calendar_details->start_date);
        $calendar_end = new DateTime($calendar_details->end_date);
        $leave_start = new DateTime($start_date);
        $leave_end = new DateTime($end_date);

        // If leave dates are outside calendar range, return 0
        if ($leave_end < $calendar_start || $leave_start > $calendar_end) {
            echo 0;
            return;
        }

        // Calculate working days excluding holidays and weekends
        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;

        $holidays = 0;
        $holiday_dates = [];

        // Get holiday dates from calendar events
        if (isset($calendar_details->events)) {
            foreach ($calendar_details->events as $event) {
                if (in_array($event->event_type, ['holiday', 'holiday_range'])) {
                    $event_start = new DateTime($event->from_date);
                    $event_end = new DateTime($event->to_date);
                    $interval = new DateInterval('P1D');
                    $event_range = new DatePeriod($event_start, $interval, $event_end->modify('+1 day'));

                    foreach ($event_range as $date) {
                        $holiday_dates[] = $date->format('Y-m-d');
                    }
                }
            }
        }

        // Count holidays and weekends
        for ($i = 0; $i < $leaveDays; $i++) {
            $date = date("Y-m-d", strtotime($start_date . " +" . $i . " days"));

            // Check if it's a holiday from calendar events
            if (in_array($date, $holiday_dates)) {
                $holidays++;
            }
            // Check if it's a Sunday (weekoff)
            else if (date('w', strtotime($date)) == 0) {
                $holidays++;
            }
        }

        echo $leaveDays - $holidays;
    }

    // Helper method to calculate days excluding weekends only
    private function _calculateDaysExcludingWeekends($start_date, $end_date) {
        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;

        $weekends = 0;
        for ($i = 0; $i < $leaveDays; $i++) {
            $date = date("Y-m-d", strtotime($start_date . " +" . $i . " days"));
            if (date('w', strtotime($date)) == 0) { // Sunday
                $weekends++;
            }
        }

        return $leaveDays - $weekends;
    }

    // Get assigned calendar details for a student
    public function getAssignedCalendarDetails() {
        if (!isset($_POST['studentId'])) {
            echo json_encode(null);
            return;
        }

        $student_id = $_POST['studentId'];

        if (empty($student_id)) {
            echo json_encode(null);
            return;
        }

        // Get student's section ID
        $section_id = $this->Student_leave_model->getStudentSectionId($student_id);

        if (!$section_id) {
            echo json_encode(null);
            return;
        }

        // Load the Student_Model to get assigned calendar details
        $this->load->model('student/Student_Model');
        $calendar_details = $this->Student_Model->getassignedcalendardetails($section_id);

        if ($calendar_details) {
            // Format the response
            $response = array(
                'id' => $calendar_details->id,
                'calendar_name' => $calendar_details->calendar_name,
                'start_date' => $calendar_details->start_date,
                'end_date' => $calendar_details->end_date,
                'target_group' => $calendar_details->target_group
            );
            echo json_encode($response);
        } else {
            echo json_encode(null);
        }
    }

    public function studentLeaveSubmit() {
        // $AvatarId = $this->authorization->getAvatarId();
        // $avatar = $this->avatar->getAvatarById($AvatarId);
        // $data['filedBy'] = $avatar->stakeholderId;
        // $data['getStudentName'] = $this->Student_leave_model->getStudentNameInfo();
        $parent_id = $this->authorization->getAvatarStakeHolderId();
        $data['student_id'] = $this->Student_leave_model->getStudentId($parent_id);
        // $data['getStudentClassSection'] = $this->Student_leave_model->getStudentClassSectionInfo($data['student_id']);
        // $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaveInfo($parent_id);
        $data['main_content'] = 'student/student_leave/apply_student_leave_index';
    
        $this->load->view('inc/template', $data);
    }
    public function s3FileUpload($file) {

        if ($file['tmp_name'] == '' || $file['name'] == '') {
            return ['status' => 'empty', 'file_name' => ''];
        }

        $this->load->library('filemanager');
        return $this->filemanager->uploadFile($file['tmp_name'], $file['name'], 'leave');
    }

    public function studentApplyedLeaveStatus() {
        $AvatarId = $this->authorization->getAvatarId();
        $avatar = $this->avatar->getAvatarById($AvatarId);
        $input_form = $this->input->post();
        $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentLeaveInfo($avatar->stakeholderId);

        // Validate against assigned calendar before processing
        $student_id = $input_form['student_id'];
        $from_date = $input_form['from_date'];
        $to_date = $input_form['to_date'];

        if ($student_id && $from_date && $to_date) {
            // Get student's section ID
            $section_id = $this->Student_leave_model->getStudentSectionId($student_id);

            if ($section_id) {
                $this->load->model('student/Student_Model');
                $calendar_details = $this->Student_Model->getassignedcalendardetails($section_id);

                if ($calendar_details) {
                    $fromdate = date("Y-m-d", strtotime($from_date));
                    $todate = date("Y-m-d", strtotime($to_date));

                    $calendar_start = new DateTime($calendar_details->start_date);
                    $calendar_end = new DateTime($calendar_details->end_date);
                    $leave_start = new DateTime($fromdate);
                    $leave_end = new DateTime($todate);

                    if ($leave_end < $calendar_start || $leave_start > $calendar_end) {
                        $this->session->set_flashdata('flashError', 'Leave dates are outside the assigned calendar range (' .
                                                     date('d-m-Y', strtotime($calendar_details->start_date)) . ' to ' .
                                                     date('d-m-Y', strtotime($calendar_details->end_date)) . ')');
                        redirect('student/Student_leave_controller/index');
                        return;
                    }
                }
            }
        }

        $status =(int) $this->Student_leave_model->checkAlreadyTaken(null);
        if($status == 0){
            $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
            redirect('student/Student_leave_controller/index');
        }

        $storeFullStudentDetails = $this->Student_leave_model->addStudentLeaveInfo($input_form, $this->s3FileUpload($_FILES['file_upload']), 0);
        // echo "<pre>";print_r($storeFullStudentDetails);die();
        if ($storeFullStudentDetails == '1') {

            $this->session->set_flashdata('flashSuccess', 'Leave Applied Successfully');
            redirect('student/Student_leave_controller/index');
        } elseif ($storeFullStudentDetails == 'errors') {
            $this->session->set_flashdata('flashError', 'upload doctor certificate..');
            redirect('student/Student_leave_controller/studentApplyedLeaveStatus');

        } else {
            //echo "<pre>";print_r($storeFullStudentDetails);die();
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('student/Student_leave_controller/index');
        }

    }

    //get holiday count to deduct from number of leaves
    public function getHolidayCount() {
        $start_date = $_POST['startDate'];
        $end_date = $_POST['endDate'];

        $start_date_formatted = strtotime($start_date);
        $end_date_formatted = strtotime($end_date);
        $datediff = $end_date_formatted - $start_date_formatted;
        $leaveDays = ($datediff / (60 * 60 * 24)) + 1;

        $holidays = 0;
        for($i = 0; $i < $leaveDays; $i++){
            $date = date("Y-m-d", strtotime($start_date." +".$i." days"));
            $val = $this->Student_leave_model->getHoliday($date);
            if($val > 0){
                $holidays++;
            }
            if($val == 0 && date('w', strtotime($date)) == 0){
                $holidays++;
            }
        }
        echo $leaveDays - $holidays;
    }

    //edit student leave
    public function editStudentLeave($id){
        $data['getStudentName'] = $this->Student_leave_model->getStudentNameInfo();
        $data['getStudentClassSection'] = $this->Student_leave_model->getStudentClassSectionInfo($data['getStudentName']->std_id);
        $data['getFullStudentDetail'] = $this->Student_leave_model->getStudentInfoByLeaveId($id);
        if ($this->mobile_detect->isTablet()) {
             $data['main_content'] = 'student/student_leave/edit_student_leave_tablet';
        }else if($this->mobile_detect->isMobile()){
            $data['main_content'] = 'student/student_leave/edit_student_leave_mobile';
        }else{
            $data['main_content'] = 'student/student_leave/edit_student_leave';   
        }
        $this->load->view('inc/template', $data);
    }

    //update student leave
    public function updateStudentLeave($id){
        $input_form = $this->input->post();
        $status =(int) $this->Student_leave_model->checkAlreadyTaken($id);
        if($status == 0){
            $this->session->set_flashdata('flashError', 'Leave already taken on this date.');
            redirect('student/Student_leave_controller/index');
        }
        $storeFullStudentDetails = $this->Student_leave_model->addStudentLeaveInfo($input_form, $this->s3FileUpload($_FILES['file_upload']), $id);

        if ($storeFullStudentDetails == '1') {

            $this->session->set_flashdata('flashSuccess', 'Leave Updated Successfully');
            redirect('student/Student_leave_controller/index');
        } elseif ($storeFullStudentDetails == 'errors') {
            $this->session->set_flashdata('flashError', 'upload doctor certificate..');
            
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('student/Student_leave_controller/index');
        }
    }

    // //delete applied leave
    // public function delete_leave($id){
    //     $status = (int) $this->Student_leave_model->deleteLeaveInfo($id);

    //     if ($status == 1) {
    //         $this->session->set_flashdata('flashSuccess', 'Leave Deleted Successfully');
    //         redirect('student/Student_leave_controller/leaveReport');
    //     } else {
    //         $this->session->set_flashdata('flashError', 'Something Wrong..');
    //         redirect('student/Student_leave_controller/leaveReport');
    //     }
    // }

    //delete applied leave
    public function delete_leave_parent($id){
        $status = (int) $this->Student_leave_model->deleteLeaveInfo($id);

        if ($status == 1) {
            $this->session->set_flashdata('flashSuccess', 'Leave Deleted Successfully');
            redirect('student/Student_leave_controller/index');
        } else {
            $this->session->set_flashdata('flashError', 'Something Wrong..');
            redirect('student/Student_leave_controller/index');
        }
    }

    //allow admin to apply student leaves
    // public function LeaveReport(){
    //     $AvatarId = $this->authorization->getAvatarId();
    //     $avatar = $this->avatar->getAvatarById($AvatarId);
    //     $staffId = $avatar->stakeholderId;
    //     $data['leavesApplied'] = $this->Student_leave_model->get_Leaves($staffId,1);
    //     $data['main_content'] = 'student/student_leave/admin_applied/index';
    //     $this->load->view('inc/template', $data);
    // }

    // public function applyStudentLeave(){
    //     $data['getclassinfo'] = $this->Student_Model->getclass();
    //     $data['main_content'] = 'student/student_leave/admin_applied/apply_leave';
    //     $this->load->view('inc/template', $data);
    // }

    // public function applyStudentLeave_edit($id){
    //     $data['edit_student_leave'] = $this->Student_leave_model->get_apply_student_leave_by_id($id);
    //     // echo "<pre>"; print_r($data['edit_student_leave']); die();
    //     $data['getclassinfo'] = $this->Student_Model->getclass();
    //     $data['main_content'] = 'student/student_leave/admin_applied/apply_leave_edit';
    //     $this->load->view('inc/template', $data);
    // }

    // public function get_student() {
    //     $sectionId = $this->input->post('section');
    //     echo json_encode($this->Student_leave_model->getStudentsBySection($sectionId));
    // }

    public function submitAdminAppliedLeave(){
        // $AvatarId = $this->authorization->getAvatarId();
        // $avatar = $this->avatar->getAvatarById($AvatarId);
        // $staffId = $avatar->stakeholderId;

        $status = (int)$this->Student_leave_model->addAdminAppliedLeave($this->s3FileUpload($_FILES['file_upload']));
        if ($status == 1) {
            $this->session->set_flashdata('flashSuccess', 'Leave Applied Successfully');
            redirect('student/Student_leave_controller/LeaveReport');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            redirect('student/Student_leave_controller/LeaveReport');
        }
    }

     public function updateAdminAppliedLeave($id){
        // $AvatarId = $this->authorization->getAvatarId();
        // $avatar = $this->avatar->getAvatarById($AvatarId);
        // $staffId = $avatar->stakeholderId;

        $status = (int)$this->Student_leave_model->addAdminAppliedLeave($this->s3FileUpload($_FILES['file_upload']), $id);
        if ($status == 1) {
            $this->session->set_flashdata('flashSuccess', 'Leave Applied Successfully');
            redirect('student/Student_leave_controller/LeaveReport');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            redirect('student/Student_leave_controller/LeaveReport');
        }
    }

    public function deleteFiledLeave($stdId){
        $status = (int)$this->Student_leave_model->delete();
        if ($status == 1) {
            $this->session->set_flashdata('flashSuccess', 'Leave Applied Successfully');
            redirect('student/Student_leave_controller/LeaveReport');
        } else {
            $this->session->set_flashdata('flashError', 'Something Went Wrong..');
            redirect('student/Student_leave_controller/LeaveReport');
        }
    }

   

}
