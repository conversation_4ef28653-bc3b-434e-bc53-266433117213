<div class="col-md-12">
    
        <div class="card cd_border" style="width: 100%">
            <div class="card-header panel_heading_new_style_staff_border">
                 <div class="row" style="margin: 0px">
                    <h3 class="card-title panel_title_new_style_staff">
                        <a class="back_anchor" href="<?php echo site_url('student/Student_leave_controller/index'); ?>" class="control-primary">
                          <span class="fa fa-arrow-left"></span>
                        </a> 
                        Apply Leave
                    </h3>
                </div>
            </div>
            <form enctype="multipart/form-data" autocomplete="off" method="post" id="leave-form" action="<?php echo site_url('student/Student_leave_controller/studentApplyedLeaveStatus'); ?>" data-parsley-validate="" class="form-horizontal" >
                <div class="card-body">
                    <?php $notes=$this->settings->getSetting('student_leave_header_notes');
                    if(!empty($notes)){ ?>
                        <p><strong>Notes:</strong><br><?php echo $notes ?><br></p>
                    <?php } ?>
                        <input type="hidden" value="<?php echo $student_id; ?>" name="student_id" id="student_id">

                        <div class="form-group row">
                            <label class="control-label col-md-3 text-right" for="request_date">Request Date : </label>
                            <div class="col-md-4" >
                                <input type="text" class="form-control" readonly value="<?php echo date('d-m-Y'); ?>">
                            </div>
                            
                            <input type="hidden" value="<?php echo date('d-m-Y'); ?>" class="form-control"  id="requestdateId" name="request_date" >
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="from_date">From Date :<font color="red"> *</font></label>
                            <div class="col-md-4"> 
                                <div class="input-group date" id="start_date_picker"> 
                                    <input placeholder="Enter From Date" required="" type="text"  class="form-control " id="fromdateId" name="from_date" >
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date" >To Date :<font color="red"> *</font></label>
                            <div class="col-md-4">
                                <div class="input-group date" id="end_date_picker">
                                    <input placeholder="Enter To Date" required="" type="text" class="form-control " id="todateId" name="to_date">
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Calendar warning will appear here -->
                        <div class="form-group row" id="calendar-warning-container" style="display: none;">
                            <div class="col-md-3"></div>
                            <div class="col-md-8">
                                <div id="calendar-warning" class="alert alert-warning" style="margin: 0; padding: 8px 12px; font-size: 12px;">
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="no_of_days">No. of Days :<font color="red"> *</font></label>
                            <div class="col-md-4"> 
                                <input type="number" class="form-control" required="" placeholder="Number of days" id="noofdaysId" name="noofdays" value="" min="1">
                                <span class="help-block text-muted">Enter the number of leave days barring holidays and weekly off</span>

                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Upload File</label>
                            <div class="col-md-4"> 
                                <div class="input-group"> 
                                    <input type="file" class="form-control " id="file_upload" name="file_upload" data-parsley-error-message="Upload Doctor Certificate if  more than 3 days Sick leave !">
                                </div>
                                 <span class="help-block text-muted">Upload Doctor Certificate for more than 3 days leave (Only pdf allowed) </span>
                            </div>
                           <!--  <div class="col-md-2" style="color:black;"><small>Allowed pdf<small></div> -->
                        </div>
                    
                        <!-- <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Leave Type</label>
                            <div class="col-md-4">
                                
                            <label class="radio-inline"><input type="radio"  value="Sick"  name="leave_type" required="">Sick</label>
                            <label class="radio-inline"><input checked type="radio"  required="" value="Other" type="radio" name="leave_type" required="">Other</label>
                    
                            </div>
                        </div> -->

                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="leave_type">Leave Type: <font color="red">*</font></label>
                            <div class="col-md-4">
                                <select class="form-control" id="leave_type" name="leave_type">
                                <option value="">Select Leave Type</option>
                                <option value="Sick">Sick</option>
                                <option value="Medical">Medical</option>
                                <option value="Doctor's Appointment">Doctor's Appointment</option>
                                <option value="Travel">Travel</option>
                                <option value="Visa Appointment">Visa Appointment</option>
                                <option value="Family Function">Family Function</option>
                                <option value="Other">Others</option>
                                </select>
                            </div>
                        </div>
                    
                        <div class="form-group row">
                            <label class="col-md-3 control-label" for="to_date">Reason</label>
                            <div class="col-sm-4">  
                                <textarea placeholder="Enter Reason" rows="4" class="form-control" name="reason"></textarea>
                            </div>
                        </div>
                </div>
                <center>
                    <div class="card-footer">
                        <input type="submit" style="width: 120px;" class="btn btn-primary" onclick="loader()">
                        <a href="<?php echo site_url('student/Student_leave_controller/index'); ?>" style="width: 120px;" class='btn btn-warning mrg'>Cancel</a>
                    </div>
                </center>
            </form>
        </div>
</div>

<div class="visible-xs">
  <a href="<?php echo site_url('student/Student_leave_controller/index');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script type="text/javascript">
    function convert($str) {
    var date = new Date($str),
        mnth = ("0" + (date.getMonth()+1)).slice(-2),
        day  = ("0" + date.getDate()).slice(-2);
    return [ date.getFullYear(), mnth, day ].join("-");
}
</script>
<script type="text/javascript">
    $(document).ready(function () {
        var startDate = null;
        var endDate = null;

    $('input[name=sick_other]').change(function(){
        var value = $( 'input[name=sick_other]:checked' ).val();
        var days = $('#noofdaysId').val();
        if(value === 'Sick' && days > 3){
           $("#file_upload").prop("required", true);
        }
        else {
            $("#file_upload").prop("required", false);
        }
    });

    $('#start_date_picker').datepicker({
        format: 'd-m-yyyy',
        startDate: new Date(),
        "autoclose": true
      })
      .on('changeDate', function (selected) {
        startDate = new Date(selected.date.valueOf());
        var date2 = $('#start_date_picker').datepicker('getDate');
        date2.setDate(date2.getDate());
        $('#end_date_picker').datepicker('setDate', date2);
        //sets minDate to dt1 date + 1
        $('#end_date_picker').datepicker('setStartDate', date2);

       if (startDate != null|| endDate !=null){
       var formatedstartdate=convert(startDate);
       var formateendate=convert(endDate);
        myFunc(formatedstartdate, formateendate);
        }
    });
    $('#end_date_picker').datepicker({
        format: 'd-m-yyyy',
        "autoclose": true
    })
    .on('changeDate', function (selected) {
        endDate = new Date(selected.date.valueOf());
    var formatedstartdate=convert(startDate);
    var formateendate=convert(endDate);
    if (startDate != null|| endDate !=null)
        {
        myFunc(formatedstartdate, formateendate);
        } 
    });

    $('#file_upload').on('change', function(){
        if(this.files[0].size < 1000000){
            var file = $(this).val();
            if(file == '') return false;
            var ext = file.substr(file.lastIndexOf('.') + 1).toLowerCase();
            var allowedExt = ['pdf'];
            if(ext.length <= 0){
                alert('Wrong file format, Allowed formats (pdf)');
                $(this).val() = '';
                return false;
            }
            else {
                if(allowedExt.indexOf(ext) === -1){
                    alert('Wrong file format, Allowed formats (pdf)');
                    $(this).val('');
                    return false;
                }
            }
        } else{
            alert("File is too big! Please upload with in 1MB");
            this.value = "";
            return false;
        }
        
    });
});
</script>

 <script type="text/javascript">
    function myFunc(startDate, endDate) {
        if (startDate ==  "1970-01-01" || endDate == "1970-01-01")
            return false;

        var studentId = $("#student_id").val();
        if (!studentId) {
            $("#noofdaysId").val(0);
            return false;
        }

        // Use calendar v2 system for holiday calculation
        $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCountV2'); ?>', {
            startDate: startDate,
            endDate: endDate,
            studentId: studentId
        }, function (data) {
            var leaveholidayCount = data;
            $("#noofdaysId").val(parseInt(leaveholidayCount));
        });
    }

    // Check assigned calendar when page loads
    $(document).ready(function() {
        var studentId = $("#student_id").val();
        if (studentId) {
            checkAssignedCalendar(studentId);
        }
    });

    // Function to check assigned calendar and set date restrictions
    function checkAssignedCalendar(studentId) {
        if (!studentId) return;

        $.post("<?php echo site_url('student/Student_leave_controller/getAssignedCalendarDetails');?>", {studentId: studentId}, function(data) {
            try {
                var calendarData = $.parseJSON(data);
                if (calendarData && calendarData.start_date && calendarData.end_date) {
                    // Update date picker restrictions based on calendar range
                    var startDate = new Date(calendarData.start_date);
                    var endDate = new Date(calendarData.end_date);

                    // Update the date pickers with calendar restrictions
                    $('#start_date_picker').datepicker('setStartDate', startDate);
                    $('#start_date_picker').datepicker('setEndDate', endDate);
                    $('#end_date_picker').datepicker('setStartDate', startDate);
                    $('#end_date_picker').datepicker('setEndDate', endDate);

                    // Show calendar warning to user
                    showCalendarWarning(calendarData);
                } else {
                    // No calendar assigned, remove restrictions
                    $('#start_date_picker').datepicker('setStartDate', new Date());
                    $('#start_date_picker').datepicker('setEndDate', null);
                    $('#end_date_picker').datepicker('setStartDate', null);
                    $('#end_date_picker').datepicker('setEndDate', null);
                    hideCalendarWarning();
                }
            } catch (e) {
                console.log('Error parsing calendar data:', e);
            }
        });
    }

    // Function to show calendar warning
    function showCalendarWarning(calendarData) {
        var warningHtml = '<i class="fa fa-exclamation-triangle" style="color: #8a6d3b; margin-right: 5px;"></i>' +
                          '<strong>Calendar Restriction:</strong> ' +
                          'Leave dates must be between <strong>' + formatDate(calendarData.start_date) + '</strong> and <strong>' + formatDate(calendarData.end_date) + '</strong> ' +
                          '(Calendar: ' + calendarData.calendar_name + ')';

        $('#calendar-warning').html(warningHtml);
        $('#calendar-warning-container').show();
    }

    // Function to hide calendar warning
    function hideCalendarWarning() {
        $('#calendar-warning-container').hide();
    }

    // Function to format date for display
    function formatDate(dateString) {
        var date = new Date(dateString);
        return date.getDate().toString().padStart(2, '0') + '-' +
               (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
               date.getFullYear();
    }
</script>

<style>
/* Calendar warning styles */
#calendar-warning-container {
    margin-bottom: 0;
}

#calendar-warning {
    background-color: #fcf8e3;
    border: 1px solid #faebcc;
    color: #8a6d3b;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

#calendar-warning strong {
    color: #8a6d3b;
}

/* Mobile responsive */
@media (max-width: 768px) {
    #calendar-warning {
        font-size: 11px !important;
        padding: 6px 8px !important;
    }

    #calendar-warning-container .col-md-8 {
        padding-left: 5px;
        padding-right: 5px;
    }
}

.fa-exclamation-triangle {
    color: #8a6d3b;
}
</style>


<script>

function loader(){
    var $form = $('#leave-form');
    if ($form.parsley().validate()){       
        document.getElementById('loader').style.display="block";
        document.getElementById('myBtn').style.display="none";
        $('#leave-form').submit(); 
    }
}
</script>