<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('leave_controller');?>">Leave</a></li>
    <li><a href="<?php echo site_url('leave_controller/student_leave');?>">Student leave Info</a></li>
    <li class="active">Apply Leave</li>
</ul>


<div class="col-md-12">
    <div class="panel panel-default new-panel-style_3">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px;">
                <div class="col-md-9 pl-0">
                    <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('leave_controller/student_leave');?>"><span class="fa fa-arrow-left"></span></a>Leave Application </h3>
                </div>
                <div class="col-md-3 pr-0">
                   <span class="pull-right"><strong><?php echo date('d-m-Y'); ?></strong></span>
                </div>
            </div>
        </div>
       <form enctype="multipart/form-data" method="post" id="demo-form" action="<?php echo site_url('leave_controller/submit_student_Leave_for_staff'); ?>" data-parsley-validate="" class="form-horizontal" >
            <div class="card-body pt-1 pb-3">
                <p>
                <strong>Notes: </strong><br>
                1. To apply leave, please fill the form below.<br>
                2. Select the class, section, enter the date range and add the reason for the leave<br>
                3. Click the Submit button. 
                </p><br><br>

                <div class="form-group row">
                    <label class="col-md-4 control-label">Select Class  <font color="red">*</font></label>
                    <div class="col-sm-4">
                    <select id="class_id" name="class_id" class="form-control input-md">
                    <option value=""><?php echo 'Select Class ' ?></option>
                        <?php foreach ($getclassinfo as $value) { ?>
                            <option value="<?php echo $value->id; ?>"><?php echo $value->class_name; ?></option>
                        <?php }?>
                    </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-4 control-label" for="sections">Section: <font color="red">*</font></label>
                    <div class="col-md-4">
                        <select   disabled="true"  class="form-control" id="section_dtails" name="section">
                        <option value="">Select Section</option>
                        </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-4 control-label" for="BookType">Student: <font color="red">*</font></label>
                    <div class="col-md-4">
                    <select title="Select Student" disabled="true"   name="student_id" id="student_details_info" class="form-control ">
                        <option value="">Select Student</option>
                    </select>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="control-label col-sm-4" for="from_date">From Date <font color="red">*</font></label>
                    <div class="col-sm-4"> 
                        <div class="input-group date" id="start_date_picker"> 
                            <input placeholder="Enter From Date" autocomplete="off" required="" type="text"  class="form-control " id="fromdateId" name="from_date" >
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="control-label col-sm-4" for="to_date">To Date</label>
                    <div class="col-sm-4"> 
                        <div class="input-group date" id="end_date_picker"> 
                            <input placeholder="Enter To Date" autocomplete="off" required="" type="text" class="form-control " id="todateId" name="to_date">
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="control-label col-sm-4" for="no_of_days">No. of Days</label>
                    <div class="col-sm-4"> 
                        <input type="number" class="form-control" id="noofdaysId" readonly="" name="noofdays" value="" min="1">
                    </div>
                </div>

                <div class="form-group row">
                    <label class="control-label col-md-4" for="to_date">Upload File</label>
                    <div class="col-md-4"> 
                        <div class="input-group"> 
                            <input type="file" class="form-control" id="file_upload" name="file_upload" data-parsley-error-message="Upload Doctor Certificate if  more than 3 days Sick leave !">
                        </div>
                         <span class="text-muted">Allowed pdf|jpeg|jpg</span>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-md-4 control-label" for="leave_type">Leave Type: <font color="red">*</font></label>
                    <div class="col-md-4">
                        <select class="form-control" id="leave_type" required="" name="leave_type">
                        <option value="">Select Leave Type</option>
                        <option value="Sick">Sick</option>
                        <option value="Medical">Medical</option>
                        <option value="Doctor's Appointment">Doctor's Appointment</option>
                        <option value="Travel">Travel</option>
                        <option value="Visa Appointment">Visa Appointment</option>
                        <option value="Family Function">Family Function</option>
                        <option value="Other">Others</option>
                        </select>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="control-label col-md-4" for="to_date">Reason</label>
                    <div class="col-md-4">  
                        <textarea placeholder="Enter Reason" class="form-control" name="reason"></textarea>
                    </div>
                </div>

                <center>
                    <input type="submit" class="btn btn-primary">
                    <a href="<?php echo site_url('leave_controller/student_leave');?>" class='btn btn-warning mrg'>Cancel</a>
                </center>
            </div>
        </form>
    </div>
</div>


<div class="visible-xs visible-sm">
  <a href="<?php echo site_url('student/Student_leave_controller/leaveReport');?>" id="backBtn" onclick="loader()"><span class="fa fa-mail-reply"></span></a>
</div>

<script type="text/javascript">
    function convert($str) {
    var date = new Date($str),
        mnth = ("0" + (date.getMonth()+1)).slice(-2),
        day  = ("0" + date.getDate()).slice(-2);
    return [ date.getFullYear(), mnth, day ].join("-");
}
</script>
<script type="text/javascript">
$(document).ready(function () {
    var startDate = null;
    var endDate = null;

    $('input[name=sick_other]').change(function(){
        var value = $( 'input[name=sick_other]:checked' ).val();
        var days = $('#noofdaysId').val();
        if(value === 'Sick' && days > 3){
            $("#file_upload").prop("required", false);
            // $("#file_upload").prop("required", true);
        }
        else {
            $("#file_upload").prop("required", false);
        }
    });
    let applyStudentLeaveForPreviousDates = "<?php echo $applyStudentLeaveForPreviousDates; ?>";
    if(applyStudentLeaveForPreviousDates == 1){
        $('#start_date_picker').datepicker({
            format: 'd-m-yyyy',
            "autoclose": true
        }).on('changeDate', function (selected) {
            startDate = new Date(selected.date.valueOf());
            var date2 = $('#start_date_picker').datepicker('getDate');
            date2.setDate(date2.getDate());
            $('#end_date_picker').datepicker('setDate', date2);
            //sets minDate to dt1 date + 1
            $('#end_date_picker').datepicker('setStartDate', date2);
            if (startDate != null|| endDate !=null){
                var formatedstartdate=convert(startDate);
                var formateendate=convert(endDate);
                myFunc(formatedstartdate, formateendate);
            }
        });
    } else {
        $('#start_date_picker').datepicker({
            format: 'd-m-yyyy',
            startDate: new Date(),
            "autoclose": true
        }).on('changeDate', function (selected) {
            startDate = new Date(selected.date.valueOf());
            var date2 = $('#start_date_picker').datepicker('getDate');
            date2.setDate(date2.getDate());
            $('#end_date_picker').datepicker('setDate', date2);
            //sets minDate to dt1 date + 1
            $('#end_date_picker').datepicker('setStartDate', date2);
            if (startDate != null|| endDate !=null){
                var formatedstartdate=convert(startDate);
                var formateendate=convert(endDate);
                myFunc(formatedstartdate, formateendate);
            }
        });
    }

    $('#end_date_picker').datepicker({
        format: 'd-m-yyyy',
        "autoclose": true
    })
    .on('changeDate', function (selected) {
        endDate = new Date(selected.date.valueOf());
        var formatedstartdate=convert(startDate);
        var formateendate=convert(endDate);
        if (startDate != null|| endDate !=null) {
            myFunc(formatedstartdate, formateendate);
        } 
    });

    $('#file_upload').on('change', function(){
        var file = $(this).val();
        if(file == '') return false;
        var ext = file.substr(file.lastIndexOf('.') + 1).toLowerCase();
        var allowedExt = ['pdf', 'jpeg', 'jpg'];
        if(ext.length <= 0){
            alert('Wrong file format, Allowed formats (pdf, jpeg and jpg)');
            $(this).val() = '';
            return false;
        }
        else {
            if(allowedExt.indexOf(ext) === -1){
                alert('Wrong file format, Allowed formats (pdf, jpeg and jpg)');
                $(this).val('');
                return false;
            }
        }
    });
});
</script>

<script type="text/javascript">
    function myFunc(startDate, endDate) {
        if (startDate ==  "1970-01-01" || endDate == "1970-01-01")
            return false;
        $.post('<?php echo site_url('student/Student_leave_controller/getHolidayCount'); ?>', {startDate: startDate, endDate: endDate}, function (data) {
            var leaveholidayCount = data;
      
            $("#noofdaysId").val(parseInt(leaveholidayCount));
        });
    }
</script>

<script>
$("#class_id").change(function(){
    var className=$("#class_id").val();
    $.post("<?php echo site_url('leave_controller/get_section');?>",{className:className},function(data){
      var resultData=$.parseJSON(data);
      var output='';
      var section = resultData.section;

       output+='<option value="">Select Section</option>';
       for (var i=0,j=section.length; i < j; i++) {
        output+='<option value="'+section[i].id+'">'+section[i].section_name+'</option>';
       }
        $("#section_dtails").html(output);
        $("#section_dtails").prop('disabled',false);
    });
});

$("#section_dtails").change(function(){
    var className=$("#class_id").val();
    var section=$("#section_dtails").val();
    $.post("<?php echo site_url('leave_controller/get_student');?>",{section:section},function(data){
      var resultData=$.parseJSON(data);
      var output1='';
      var stdName = resultData;

       output1+='<option value="">Select Student</option>';
       for (var i=0,j=stdName.length; i < j; i++) {
         output1+='<option value="'+stdName[i].id+'">'+stdName[i].std_name+' </option>';
       }
        $("#student_details_info").html(output1);
        $("#student_details_info").prop('disabled',false);
    });
});
</script>

